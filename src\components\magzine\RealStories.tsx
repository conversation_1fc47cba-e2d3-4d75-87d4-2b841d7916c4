"use client";
import React, { useRef } from "react";
import { HiOutlineArrowSmLeft, HiOutlineArrowSmRight } from "react-icons/hi";
import Slider from "react-slick";
import RealStoryCard from "./RealStoryCard";
import { realStoriesData } from "@/data/magzine";

const RealStories = () => {
  const sliderRef = useRef<any>(null);

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 3, // default desktop
    slidesToScroll: 1,
    arrows: false, // we will use custom arrows
    responsive: [
      {
        breakpoint: 1024, // tablets
        settings: { slidesToShow: 2 },
      },
      {
        breakpoint: 640, // mobile
        settings: { slidesToShow: 1 },
      },
    ],
  };

  return (
    <div className="space-y-14 py-16 md:px-18 sm:px-5">
      <div className="relative overflow-hidden space-y-10">
        <div className="flex justify-between items-center ">
           <h1 className="md:text-[46px] sm:text-2xl font-bold text-[#1F2630]">
            Real Stories. Real Growth.
          </h1>
          <div className="flex gap-4">
            <button
              onClick={() => sliderRef.current?.slickPrev()}
              className="md:p-3 sm:p-2 rounded-full border-2 border-[#1F2630] hover:bg-[#1F2630] hover:text-white transition-all duration-300 cursor-pointer"
            >
              <HiOutlineArrowSmLeft size={25} />
            </button>
            <button
              onClick={() => sliderRef.current?.slickNext()}
              className="md:p-3 sm:p-2 rounded-full border-2 border-[#1F2630] hover:bg-[#1F2630] hover:text-white transition-all duration-300 cursor-pointer"
            >
              <HiOutlineArrowSmRight size={25} />
            </button>
          </div>
        </div>
        <Slider ref={sliderRef} {...settings}>
          {realStoriesData.map((stories) => (
            <div key={stories.id} className="px-3 py-8">
              <RealStoryCard stories={stories} />
            </div>
          ))}
        </Slider>
      </div>
    </div>
  );
};

export default RealStories;
