"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
const Footer = () => {
  const router = useRouter();
  return (
    <div className="relative w-full h-auto text-white overflow-hidden bg-[#202D40]">
      <Image
        src="/commom/footer_bg.svg"
        alt="Footer Background"
        fill
        className="object-cover object-center z-0"
      />

      <div className="relative z-10 py-10 px-20">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-10">
          <div className="space-y-8">
            <div className="text-lg font-bold flex items-center space-x-2 cursor-pointer" onClick={() => router.push("/")}>
              <Image src="/commom/logo.svg" alt="Logo" width={55} height={55} />
              <p className="font-semibold text-[20px]"><PERSON><PERSON>folio</p>
            </div>
            <p>
              Piggy Portfolio is an investing simulator and learning platform
              that helps you practice, learn, and build confidence before
              putting real money in the market.
            </p>
          </div>

           <div className="flex flex-col   md:items-center">
            <div className="flex flex-col space-y-8">
            <Link href="/mission" className="hover:text-gray-400 font-semibold">
              Mission
            </Link>
            <Link href="/magazine" className="hover:text-gray-400 font-semibold">
              Magazine
            </Link>
            <Link href="/about-us" className="hover:text-gray-400 font-semibold">
              About us
            </Link>
            </div>
          </div>

          <div className="flex flex-col space-y-3">
            <p className="text-white font-semibold">Contact us</p>
            <p className="text-[#4285F4]"><EMAIL></p>
            <p className="text-[#4285F4]">+1 48789234</p>
          </div>

          <div className="flex flex-col space-y-4 w-fit">
            <p className="text-white font-semibold">Download the app</p>
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-3 bg-[#1F2630] rounded-xl px-5 py-3 cursor-pointer hover:scale-105 transition-transform">
                <Image
                  src="/mainpage/play_store.svg"
                  alt="Play Store"
                  width={24}
                  height={24}
                />
                <div className="text-white">
                  <p className="text-[7px]">Download Now</p>
                  <p className="font-semibold text-[14px]">Playstore</p>
                </div>
              </div>
              <div className="flex items-center gap-3 bg-[#1F2630] rounded-xl px-5 py-3 cursor-pointer hover:scale-105 transition-transform">
                <Image
                  src="/mainpage/app_store.svg"
                  alt="App Store"
                  width={24}
                  height={24}
                />
                <div className="text-white">
                  <p className="text-[7px]">Download Now</p>
                  <p className="font-semibold text-[14px]">App Store</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <p className="text-center text-[#D9D9D9] text-sm">
          Copyright - All rights reserved 2025
        </p>
      </div>
    </div>
  );
};

export default Footer;
