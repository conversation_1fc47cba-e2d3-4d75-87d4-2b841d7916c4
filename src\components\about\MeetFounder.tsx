"use client";
import Image from "next/image";
import React from "react";

const MeetFounder = () => {
  return (
    <div className="lg:px-20 md:px-10 sm:px-5  md:space-y-20 sm:space-y-6 md:py-16 sm:py-8">
      <h1 className="md:text-[46px] sm:text-[32px] font-semibold text-[#1F2630] text-center">
        Meet The Founder
      </h1>

      <div className="flex flex-col md:flex-row lg:items-start sm:items-center justify-between gap-10">
        <div className="w-[324px] h-[324px] rounded-3xl overflow-hidden">
          <Image
            src="/about/founder.jpg"
            alt="founder"
            width={500}
            height={500}
            className="w-full h-full object-cover rounded-3xl"
          />
        </div>
        <div className="flex-1 space-y-6">
          <div className="flex items-center gap-2">
            <Image
              src="/commom/logo.svg"
              alt="logo"
              width={32}
              height={32}
              className="h-8 w-auto"
            />
            <p className="font-semibold md:text-[20px] sm:text-[16px] text-[#1F2630]">
              Piggy Portfolio
            </p>
          </div>

          <h2 className="md:text-5xl sm:text-[26px] font-semibold">
            Alexia Maria Ostahi
          </h2>
          <p className="text-[#666666] md:text-2xl sm:text-xl font-medium">
            Founder
          </p>

          <p className="text-[#212121] md:text-[20px] sm:text-lg leading-relaxed">
            My name is Alexia Maria Ostahi, I am 17 years old, and I was born
            and raised in Bucharest, Romania. I am currently pursuing my
            International Baccalaureate Diploma, where I study English, History,
            and Economics at Higher Level, alongside Mathematics, Environmental
            Systems & Societies, and Spanish.
          </p>
        </div>
      </div>
      <div className="text-[18px] space-y-10 text-[#666666] leading-relaxed">
        <div className="space-y-6">
          <div className="space-y-3">
            <p className="text-black font-semibold text-2xl">My Story</p>
            <p>
              From a very young age, I was drawn to finance and economics, not
              just as subjects of study, but as tools that can change lives.
              I’ve always believed that education should never be a privilege,
              but a right. And that if we teach kids about money early, we give
              them the power to shape their own futures. With knowledge, they
              can make choices that lead to independence, stability, and
              confidence.
            </p>
          </div>
          <p>
            I was raised in a modest environment that showed me the reality of
            financial struggles many families face. It wasn’t because people
            didn’t care, it was because they had never been taught. That
            realization shaped me deeply. It taught me to approach people’s
            struggles with empathy, not judgment, and it gave me the drive to
            make a change. Later on, I also had the chance to work with children
            living in difficult conditions. Their resilience inspired me, and it
            strengthened my belief that every child deserves access to the kind
            of knowledge that can transform their future. This is one of the
            main reasons why I founded Piggy Portfolio, to give kids and teens
            the financial literacy they deserve, in a way that is engaging,
            modern, and fun.
          </p>
        </div>
        <div className="space-y-6">
          <div className="space-y-3">
            <p className="text-black font-semibold text-2xl">
              A Journey Shaped by Experience
            </p>
            <p>
              My journey has been anything but easy. Building Piggy Portfolio
              meant balancing schoolwork with entrepreneurship, often struggling
              to be taken seriously as a teenager with a big idea. Finding the
              right programmers, learning how to communicate my vision, and
              facing rejection along the way was overwhelming at times. Yet,
              each challenge became a lesson in resilience.
            </p>
          </div>
          <p>
            I was fortunate to have the support of my family. My mother taught
            me to stand tall, believe in myself, and defend my ideas, even when
            I doubted myself. My father introduced me to investing. I still
            remember the day I saw him trading, and the spark it lit inside me.
            He made me realize that finance is not just numbers on a screen, but
            a pathway to independence and achievement. My parents’ own story of
            growing up in modest households, yet studying hard to build better
            lives, inspires me every single day. I want to pass on that message
            to children: you don’t have to start at the top in life, with hard
            work, knowledge, and determination, you can go anywhere you want.
          </p>
          <p>
            I’ve also been lucky to have teachers and mentors who believed in
            me. Miss Alexandra Dache, helped me refine my pitch and supported me
            when I represented my school at Youth Forum Switzerland’s Dragon’s
            Den, where Piggy Portfolio won second place. People like Dinu
            Andreas Ewan helped me design and bring my vision to life with
            branding and materials. And of course, my team of programmers has
            been key to turning an idea into reality.
          </p>
        </div>
        <div className="space-y-6">
          <div className="space-y-3">
            <p className="text-black font-semibold text-2xl">
              Building My Path
            </p>
            <p>
              Alongside Piggy Portfolio, I’ve challenged myself in different
              arenas to grow as a leader and thinker. I’ve competed in the World
              Economics Cup, where I achieved a Top 10 worldwide placement in
              the Thinking & Innovation category and a bronze medal on the
              continental stage. I’ve expanded my skills through internships in
              banking, mergers and acquisitions, taxation, and financial
              analysis at institutions such as Erste Private Banking, BCR Social
              Finance, Rompetrol, and Biriș Goran.
            </p>
          </div>
          <p>
            But my journey has not been only about finance. At just nine years
            old, I swam at a national competition in Romania to raise funds for
            children with cancer, where together we raised $25,000. Since then,
            supporting cancer patients has remained close to my heart, and I
            continue to donate to research and patient support. In 2025, I
            delivered a TEDx talk at the Cambridge School of Bucharest
            on “Dreams vs. Doubts: The Inner Battle That Defines Us”, where I
            spoke about how often our biggest obstacle is ourselves. These
            experiences have taught me that leadership is not just about skill.
            It is about compassion, empathy, and persistence.
          </p>
        </div>
        <div className="space-y-6">
          <div className="space-y-3">
            <p className="text-black font-semibold text-2xl">
              Why Piggy Portfolio?
            </p>
            <p>
              The reason behind Piggy Portfolio is simple: kids are not taught
              about money in school. They grow up stepping into adulthood
              without the skills to manage their savings, investments, or
              financial choices. My mission is to change that.
            </p>
          </div>
          <p>
            Piggy Portfolio is designed to be a safe, engaging platform that
            makes financial literacy accessible to kids and teens between 12–18
            years old. With a stock market simulator, simplified financial news,
            educational videos, and gamified features, the app makes learning
            about money fun and approachable.
          </p>
          <p>
            My dream is for Piggy Portfolio to reach children across the world,
            so that no child enters adulthood unprepared. In the future, I hope
            to expand beyond the app, through partnerships with schools,
            courses, competitions, and non-profit initiatives, so financial
            literacy becomes as normal as learning to read or write.
          </p>
        </div>
      </div>
    </div>
  );
};

export default MeetFounder;
