"use client";
import Image from "next/image";
import React from "react";

const HeroSect = () => {
  return (
    <div>
      <div className="flex flex-col items-center pt-8 md:space-y-16 sm:space-y-5">
        <h1 className="md:text-6xl sm:text-[42px] font-semibold text-[#1F2630]  leading-relaxed">
          Our Mission
        </h1>
        <p className="md:text-lg sm:text-sm text-[#666666] text-center md:px-0 sm:px-5 italic md:w-[50%] sm:w-full">
          “Our mission is to make financial education simple, fun, and
          accessible for children. We believe that by teaching kids the basics
          of trading and money management from an early age, we can help them
          build confidence, develop smart financial habits, and prepare for a
          future full of opportunities.”
        </p>
        <div className="lg:w-[70%] sm:w-[90%] md:h-[270px] sm:h-[130px] rounded-3xl overflow-hidden shadow-[0_0_15px_0_rgba(0,0,0,0.5)]">
          <Image
            src="/mission/mission.png"
            alt="Our Mission"
            width={500}
            height={500}
            className="w-full h-full object-cover"
          />
        </div>
      </div>
    </div>
  );
};

export default HeroSect;
