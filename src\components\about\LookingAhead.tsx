"use client";
import React from "react";

const LookingAhead = () => {
  return (
    <div className="lg:px-20 md:px-10 sm:px-5 space-y-20 md:py-16 sm:py-8">
      <div
        className="w-full rounded-3xl overflow-hidden bg-cover bg-center relative"
        style={{ backgroundImage: `url(/about/looking_ahead.jpg)` }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-black/70 z-10" />

        <div className="relative z-10 text-white md:p-10 sm:p-5 space-y-6">
          <h1 className="md:text-5xl sm:text-[32px] font-semibold">
            Looking Ahead
          </h1>
          <p className="md:text-2xl sm:text-xl leading-relaxed">
            This journey is still just beginning. Piggy Portfolio is moving from
            idea to prototype, and soon into full development. My goal is to
            partner with schools and ensure that as many children as possible
            have access to financial education.
          </p>
          <p className="md:text-2xl sm:text-xl leading-relaxed">
            I know it won’t always be easy. But I’ve learned that with
            resilience, empathy, and hard work, anything is possible. <PERSON>gy
            Portfolio isn’t just an app, it’s a movement to make sure that the
            next generation grows up not only dreaming big, but also knowing how
            to build those dreams into reality.
          </p>
        </div>
      </div>
    </div>
  );
};

export default LookingAhead;
