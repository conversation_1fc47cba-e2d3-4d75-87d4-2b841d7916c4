"use client";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React from "react";

const PartnerWithUs = () => {
  const router = useRouter();
  const handleNavigate = (href: string) => {
    router.push(href);
  };
  return (
    <div className="lg:px-20 md:px-10 sm:px-5 space-y-14 md:py-16 sm:py-6">
      <div className="bg-[#E3FFF2] rounded-3xl relative overflow-hidden flex lg:flex-row sm:flex-col justify-between">
        <div className="md:p-10 sm:p-5 space-y-10 text-[#1F2630]">
          <h1 className="md:text-[46px] sm:text-[22px] font-bold text-[#1F2630] text-[46px]">
            Partner with Piggy Portfolio
          </h1>
          <div className="space-y-8">
            <p className="md:text-[18px] sm:text-[14px]">
              Empower your team or organization with financial knowledge, growth{" "}
              <br />
              tools, and exclusive access to our platform.
            </p>
            <p className="md:text-[18px] sm:text-[14px]">
              Whether you’re a company investing in your employees’ growth, an{" "}
              <br />
              organization hosting financial literacy events, or a team looking
              for <br /> tailored learning, our partnership programs are
              designed to deliver <br /> impact.
            </p>
            <button
              className="border border-[#1F2630] rounded-lg text-[#1F2630] font-semibold text-[16px] px-20 py-3 cursor-pointer"
              onClick={() => handleNavigate("/contact-us")}
            >
              Contact Us
            </button>
          </div>
        </div>
        <Image
          src="/mainpage/partner.jpg"
          alt="Partner with Piggy Portfolio"
          width={530}
          height={515}
          className="lg:h-[515px] md:w-full sm:h-[330px] flex justify-end lg:w-[530px] object-cover rounded-3xl"
        />
      </div>
    </div>
  );
};

export default PartnerWithUs;
