"use client";
import Image from "next/image";
import React from "react";

const GetToKnow = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 items-center px-6 lg:px-20 md:px-10 sm:px-5 pt-16 md:pt-36 pb-14 md:pb-20 gap-10 md:gap-16">
      <div className="order-2 lg:order-1 flex lg:justify-end md:justify-center sm:justify-end relative">
        <div className="relative w-[240px] sm:w-[230px] md:w-[370px] h-[340px] sm:h-[345px] md:h-[520px]">
          <Image
            src="/mission/mission_2.jpg"
            alt="Get To Know Us"
            width={500}
            height={500}
            className="w-full h-full object-cover rounded-2xl"
          />

          <div
            className="absolute bg-[#FB5689] rounded-[14px] sm:rounded-[18px] md:rounded-[20px] 
                          w-[80px] h-[80px] sm:w-[100px] sm:h-[100px] md:w-[155px] md:h-[155px]
                          -left-[48%] top-[8%]"
          />

          <div
            className="absolute bg-[#FB5689] rounded-[14px] sm:rounded-[18px] md:rounded-[20px]
                          w-[140px] h-[140px] sm:w-[160px] sm:h-[160px] md:w-[260px] md:h-[260px]
                          -left-[25%] bottom-[4%] -z-50"
          />
        </div>
      </div>

      <div className="order-1 lg:order-2 space-y-5 lg:text-left">
        <h1 className="text-[28px] sm:text-[32px] md:text-[46px] font-semibold text-[#1F2630] leading-snug">
          To Get To Know Us, <br className="hidden sm:block" /> Come & Meet Us
        </h1>
        <p className="text-[16px] sm:text-[14px] md:text-[18px] text-[#666666] leading-relaxed">
          We’re more than just an app—we’re a community passionate about helping
          kids understand money and trading from an early age. Our team brings
          together educators, financial experts, and designers who share one
          goal: to make learning finance fun, safe, and meaningful for children.
          Whether you’re a parent, teacher, or partner, we’d love for you to
          connect with us, explore our vision, and see how together we can shape
          brighter financial futures.
        </p>
      </div>
    </div>
  );
};

export default GetToKnow;
