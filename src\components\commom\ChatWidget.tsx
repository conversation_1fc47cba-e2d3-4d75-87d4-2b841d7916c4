"use client";
import Image from "next/image";
import { useState, useRef, useEffect } from "react";

const ChatWidget = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    { sender: "bot", text: "Welcome to Piggy Portfolio" },
    { sender: "bot", text: "How may I help you?" },
  ]);
  const [input, setInput] = useState("");
  const messagesEndRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSend = () => {
    if (input.trim() === "") return;

    setMessages([...messages, { sender: "user", text: input }]);
    setInput("");

    // Example bot reply
    setTimeout(() => {
      setMessages((prev) => [
        ...prev,
        { sender: "bot", text: "Thanks for your message! 🚀" },
      ]);
    }, 1000);
  };

  return (
    <>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-10 right-6 z-30 w-14 h-14 rounded-full bg-[#FFAEE4] flex items-center justify-center transition transform hover:scale-110 cursor-pointer"
      >
        <Image src="/commom/chat_icon.svg" alt="Chat" width={28} height={28} />
      </button>

      <div
        className={`fixed bottom-28 right-6 w-80 h-[480px] bg-white rounded-3xl shadow-[0_0_15px_0_rgba(0,0,0,0.3)] flex flex-col z-30 overflow-hidden transform transition-all duration-300 ease-in-out ${
          isOpen
            ? "opacity-100 scale-100 translate-y-0"
            : "opacity-0 scale-95 translate-y-5 pointer-events-none"
        }`}
      >
        <div className="bg-white text-[#101010] px-4 py-3 font-semibold flex justify-between items-center">
          <div className="flex items-center gap-2">
            <button className="w-8 h-8 rounded-full bg-[#FFAEE4] flex items-center justify-center">
              <Image src="/commom/chat_icon.svg" alt="Chat" width={15} height={15} />
            </button>
            <span className="font-semibold">Piggy Portfolio</span>
          </div>
          <button onClick={() => setIsOpen(false)} className="cursor-pointer">
            <Image src="/commom/close_icon.svg" alt="Close" width={18} height={18} />
          </button>
        </div>

        <div className="flex items-center justify-center">
          <div className="w-[90%] flex items-center h-[1.5px] bg-[#DDDDDD]" />
        </div>

        <div className="flex-1 p-4 overflow-y-auto text-sm text-gray-700 space-y-3">
          {messages.map((msg, index) => (
            <div
              key={index}
              className={`flex items-start gap-2 ${
                msg.sender === "user" ? "justify-end" : "justify-start"
              }`}
            >
              {msg.sender === "bot" && (
                <div className="w-6 h-6 flex-shrink-0 rounded-full bg-[#FFAEE4] flex items-center justify-center text-xs">
                  <Image
                    src="/commom/chat_icon.svg"
                    alt="Bot"
                    width={12}
                    height={12}
                  />
                </div>
              )}
              <p
                className={`px-3 py-2 rounded-lg max-w-[70%] ${
                  msg.sender === "user"
                    ? "bg-[#FFAEE4] text-white"
                    : "bg-gray-200 text-black"
                }`}
              >
                {msg.text}
              </p>
            </div>
          ))}
          <div ref={messagesEndRef}></div>
        </div>

        <div className="p-3 flex items-center gap-2 bg-[#EAEAEA]">
          <input
            type="text"
            placeholder="Type here"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            className="flex-1 rounded-lg px-3 py-2 text-sm focus:outline-none bg-transparent"
            onKeyDown={(e) => e.key === "Enter" && handleSend()}
          />
          <div className="h-6 w-[1.5px] bg-[#CCCCCC]"></div>
          <button onClick={handleSend} className="cursor-pointer">
            <Image src="/commom/send_icon.svg" alt="Send" width={25} height={25} />
          </button>
        </div>
      </div>
    </>
  );
};

export default ChatWidget;
