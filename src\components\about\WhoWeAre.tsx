"use client";
import Image from "next/image";
import React from "react";

const WhoWeAre = () => {
  return (
    <div className="grid lg:grid-cols-2 sm:grid-cols-1 items-center md:px-20 sm:px-5 md:pt-36 sm:pt-8 md:pb-20 sm:pb-8 gap-16">
      <div className="flex sm:justify-center md:justify-end">
        <div className="space-y-8">
          <h1 className="md:text-[46px] sm:text-[32px] font-semibold text-[#1F2630]">
            Who We Are?
          </h1>
          <p className="md:text-[18px] sm:text-[14px] text-[#666666]">
            We are a team of educators, financial experts, and innovators
            dedicated to making financial education simple, safe, and fun for
            children. Our app introduces kids to the world of trading through
            interactive lessons, playful courses, and engaging activities
            designed to spark curiosity and build real-life money skills.
          </p>
          <div className="flex items-center md:flex-row sm:flex-col gap-7 mt-14">
            <div className="flex items-center  gap-5 bg-[#1F2630] rounded-2xl px-7 py-4 cursor-pointer hover:scale-105 transition-transform">
              <Image
                src="/mainpage/play_store.svg"
                alt="App Store"
                width={30}
                height={30}
                className="cursor-pointer"
              />
              <div className="text-white">
                <p className="text-[8px]">Download Now</p>
                <p className="font-semibold text-[16px]">Playstore</p>
              </div>
            </div>
            <div className="flex items-center gap-5 bg-[#1F2630] rounded-2xl px-7 py-4 cursor-pointer hover:scale-105 transition-transform">
              <Image
                src="/mainpage/app_store.svg"
                alt="App Store"
                width={30}
                height={30}
                className="cursor-pointer"
              />
              <div className="text-white">
                <p className="text-[8px]">Download Now</p>
                <p className="font-semibold text-[16px]">App Store</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex lg:justify-center sm:justify-center relative">
        <div className="relative md:w-[370px] md:h-[460px] w-[280px] h-[350px] rounded-3xl shadow-[0_0_15px_0_rgba(0,0,0,0.7)]">
          <Image
            src="/about/who_1.jpg"
            alt="Get To Know Us"
            width={500}
            height={500}
            className="w-full h-full object-cover rounded-3xl"
          />
          <div className="absolute bg-[#FB5689] h-[120px] w-[120px] md:h-[155px] md:w-[155px] rounded-[20px] -right-[15%] bottom-[33%] -z-10" />
          <Image
            src="/about/who_2.png"
            alt="Get To Know Us"
            width={260}
            height={260}
            className="absolute h-[200px] w-[200px] md:h-[260px] md:w-[260px] rounded-[20px] -right-[9%] bottom-[22%] -z-10 object-cover"
          />
        </div>
      </div>
    </div>
  );
};

export default WhoWeAre;
