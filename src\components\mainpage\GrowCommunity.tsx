"use client";
import Image from "next/image";
import React, { useState } from "react";

const GrowCommunity = () => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const communityData = [
    {
      src: "/mainpage/comm_1.png",
      heading: "Community Events",
      text: "Workshops, challenges, and live webinars to learn and grow with peers."
    },
    {
      src: "/mainpage/comm_2.png",
      heading: "Networking Features",
      text: "Connect with like-minded learners, share ideas, and build lasting connections."
    },
    {
      src: "/mainpage/comm_3.png",
      heading: "User Engagement",
      text: "Stay active with discussions, polls, and fun activities that make learning exciting."
    }
  ];

  return (
    <div className="lg:px-20 md:px-10 sm:px-5  md:space-y-14 sm:space-y-6 pb-16">
      <h1 className="md:text-[46px] sm:text-[22px] font-bold text-[#1F2630]">
        A Community That Grows With You
      </h1>
      <div className="grid gap-10 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {communityData.map((item, index) => (
          <div
            key={index}
            className="bg-[#E3EFFF] rounded-3xl w-full md:h-[550px] sm:h-[427px] overflow-hidden relative group cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl"
            onMouseEnter={() => setHoveredIndex(index)}
            onMouseLeave={() => setHoveredIndex(null)}
          >
            <Image
              src={item.src}
              alt={`Community ${index + 1}`}
              width={500}
              height={500}
              className="w-full h-full object-cover rounded-3xl transition-all duration-300 group-hover:brightness-75"
            />

            {/* Hover overlay */}
            <div className={`absolute inset-0 bg-black bg-opacity-50 rounded-3xl flex flex-col justify-end p-6 transition-all duration-300 ${
              hoveredIndex === index ? 'opacity-100' : 'opacity-0'
            }`}>
              <div className="text-white">
                <h3 className="text-xl font-bold mb-2">{item.heading}</h3>
                <p className="text-sm leading-relaxed">{item.text}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default GrowCommunity;
