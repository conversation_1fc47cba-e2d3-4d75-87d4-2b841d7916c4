"use client";
import Image from "next/image";
import React from "react";

const HeroSec = () => {
  return (
    <div className="lg:px-6 md:px-10 sm:px-5 ">
      <div className="bg-gradient-to-b from-[#F3F8FF] to-[#FFFFFF] flex flex-col items-center justify-center space-y-10 sm:rounded-4xl md:rounded-none">
        <div className="text-center">
          <h1 className="md:text-6xl sm:text-[28px] font-semibold text-[#1F2630] pt-20 leading-relaxed">
            Your Gateway To The <br className="hidden md:block" />
            World Of{" "}
            <div className="inline-block space-y-1">
              <h1 className="leading-tight text-[#0165C3]">Trading</h1>
              <Image
                src="/mainpage/trade_bottom.svg"
                alt="Trading"
                width={180} // required by Next.js
                height={180}
                className="md:ml-10 sm:ml-5 w-20 sm:w-20
                 md:w-44 lg:w-[180px] h-auto"
              />
            </div>
          </h1>
        </div>
        <p className="md:text-lg sm:text-sm text-[#666666] text-center md:px-0 sm:px-5">
          Piggy Portfolio makes trading fun and educational for kids, while
          keeping <br /> parents in control and informed.
        </p>
        <div className="flex flex-col items-center relative">
          <div className="flex items-center md:flex-row sm:flex-col gap-7 z-10">
            <div className="flex items-center  gap-5 bg-[#1F2630] rounded-2xl px-7 py-4 cursor-pointer hover:scale-105 transition-transform">
              <Image
                src="/mainpage/play_store.svg"
                alt="App Store"
                width={30}
                height={30}
                className="cursor-pointer"
              />
              <div className="text-white">
                <p className="text-[8px]">Download Now</p>
                <p className="font-semibold text-[16px]">Playstore</p>
              </div>
            </div>
            <div className="flex items-center gap-5 bg-[#1F2630] rounded-2xl px-7 py-4 cursor-pointer hover:scale-105 transition-transform">
              <Image
                src="/mainpage/app_store.svg"
                alt="App Store"
                width={30}
                height={30}
                className="cursor-pointer"
              />
              <div className="text-white">
                <p className="text-[8px]">Download Now</p>
                <p className="font-semibold text-[16px]">App Store</p>
              </div>
            </div>
          </div>
          <div className="relative w-full max-w-5xl mt-6">
            <div className="absolute top-0 left-0 w-full h-40 bg-gradient-to-b from-white to-transparent z-10 blur-md"></div>
            <div className="absolute bottom-0 left-0 w-full h-28 bg-gradient-to-t from-[#F3F8FF] to-transparent z-10"></div>
            <Image
              src="/mainpage/hero_image.svg"
              alt="Hero Image"
              width={1000}
              height={1000}
              className="w-full"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSec;
