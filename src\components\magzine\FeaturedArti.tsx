"use client";
import { magzineFeatureData } from "@/data/magzine";
import Image from "next/image";
import React from "react";

const FeaturedArti = () => {
  return (
    <div className="md:px-20 sm:px-5 md:space-y-14 sm:space-y-10 md:py-16 sm:py-10">
      <h1 className="md:text-[46px] sm:text-[32px] font-semibold text-[#1F2630]">
        Featured Articles
      </h1>
      <div className="grid gap-8 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {magzineFeatureData.map((data: any, index: number) => (
          <div
            key={index}
            className="relative rounded-3xl w-full h-[550px] overflow-hidden"
          >
            <Image
              src={data.image}
              alt={data.title}
              width={500}
              height={500}
              className="w-full h-full object-cover rounded-3xl"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black via-black/40 to-transparent rounded-3xl" />
            <div className="absolute bottom-5 left-5 right-5 text-white">
              <h2 className=" md:text-2xl sm:text-[20.28px] font-semibold mb-2">
                {data.title}
              </h2>
              <p className="md:text-lg sm:text-[15.1] mb-4">
                {data.description}
              </p>
              <button className="bg-transparent text-white border border-white px-4 py-2 rounded-lg font-medium cursor-pointer sm:text-[13.52px] md:text-[16px]">
                Read More
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FeaturedArti;
