import type { Metada<PERSON> } from "next";
import { Geist, <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Navbar from "@/components/commom/Navbar";
import Footer from "@/components/commom/Footer";
import ChatWidget from "@/components/commom/ChatWidget";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  icons: [{ rel: "icon", url: "/commom/logo.svg", type: "image/png" }],
  title: "Piggy Portfolio | Landing Page",
  description: "Showcasing my work and projects",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${poppins.variable} ${geistSans.variable} antialiased`}>
        <Navbar />
        {children}
        <Footer />
        <ChatWidget />
      </body>
    </html>
  );
}
