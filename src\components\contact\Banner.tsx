"use client";
import React from "react";

const Banner = () => {
  return (
    <div className="md:px-20 sm:px-5 space-y-20 py-16">
      <div
        className="w-full md:h-[370px] sm:h-[200px] rounded-3xl overflow-hidden bg-cover bg-center relative "
        style={{ backgroundImage: `url(/contact/banner.png)` }}
      >
        <div className="absolute inset-0 bg-gradient-to-t from-black/70  to-black/40 z-10" />
        <div className="absolute inset-0 flex flex-col items-center justify-center text-white px-10 z-10">
          <div className="space-y-6 text-center max-w-4xl">
            <h2 className="md:text-6xl sm:text-[32px] font-semibold">
              Contact Us
            </h2>
            <p className="md:text-[18px] sm:text-[14px] italic">
              “Reach out to us and let’s start building brighter financial
              futures together.”
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Banner;
