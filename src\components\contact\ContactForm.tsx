"use client";
import Image from "next/image";
import React from "react";

const ContactForm = () => {
  return (
    <div className="md:px-28 sm:px-5 space-y-20 md:py-16 sm:space-y-8">
      <div className="grid md:grid-cols-2 gap-10">
        <form
          className="space-y-6"
          onSubmit={(e) => {
            e.preventDefault();
            alert("Form submitted!");
          }}
        >
          <p className="md:text-[46px] sm:text-[32px] text-[#1F2630] font-semibold">
            Fill The Data
          </p>
          <div className="space-y-2">
            <label className="block text-[16px] text-[#666666]">Username</label>
            <input
              type="text"
              className="w-full text-xl text-[#222222] font-semibold border border-gray-300 rounded-xl p-3 bg-[#F8F8F8] focus:outline-none"
              placeholder="Your Name"
              required
            />
          </div>
          <div className="space-y-2">
            <label className="block text-[16px] text-[#666666]">Email</label>
            <input
              type="email"
              className="w-full text-xl text-[#222222] font-semibold border border-gray-300 rounded-xl p-3 bg-[#F8F8F8] focus:outline-none"
              placeholder="Your Email"
              required
            />
          </div>
          <div className="space-y-2">
            <label className="block text-[16px] text-[#666666]">
              Description
            </label>
            <textarea
              className="w-full text-xl text-[#222222] font-semibold border border-gray-300 rounded-2xl p-3 bg-[#F8F8F8] focus:outline-none resize-none"
              rows={5}
              placeholder="Type..."
              required
            ></textarea>
          </div>
          <button
            type="submit"
            className="md:w-fit sm:w-full text-xl px-16 text-[#222222] font-semibold hover:bg-[#222222] hover:text-white py-3 rounded-xl border border-[#222222] cursor-pointer transition"
          >
            Submit
          </button>
        </form>
        <div className="flex items-center justify-center">
          <Image
            src="/contact/form.svg"
            alt="Contact Us"
            width={500}
            height={500}
          />
        </div>
      </div>
    </div>
  );
};

export default ContactForm;
