"use client";
import Image from "next/image";
import React from "react";

const Community = () => {
  return (
    <div className="flex flex-col items-center justify-center md:space-y-14 sm:space-y-6 lg:px-20 md:px-10 sm:px-5 md:py-24 sm:py-14">
      <div className="flex items-center justify-center md:gap-3 sm:gap-2 text-3xl font-semibold uppercase md:text-[50px] sm:text-[24px] text-[#2A2A2A]  ">
        <h1>Learn</h1>
        <p className="ml-2">.</p>
        <h1>Trade</h1>
        <p className="ml-2">.</p>
        <div className="py-2 px-3 rounded-md bg-[#202D40] text-white">
          <h1>Grow</h1>
        </div>
      </div>
      <p className="font-medium md:text-xl sm:text-sm text-[#666666]">
        The Future of Smart Investors Starts Young.
      </p>
      <div className="w-full bg-gradient-to-r from-[#202D40] to-[#253E62] relative flex items-center justify-center rounded-2xl overflow-hidden mt-8">
        <Image
          src="/commom/community_bg.svg"
          alt="Center Image"
          width={500}
          height={500}
          className="absolute inset-0 w-full h-full object-cover z-0"
        />

        <div className="relative flex flex-col items-center justify-center text-white z-10 px-4 py-10">
          <h1 className="md:text-4xl sm:text-[22px] font-semibold text-center">
            A Growing Global Community
          </h1>

          <div className="flex md:flex-row flex-col items-center justify-center gap-12 md:gap-48 mt-10 w-full">
            <div className="text-center space-y-2">
              <p>Active Members</p>
              <h1 className="font-semibold text-[42px] md:text-[68px]">81k</h1>
            </div>
            <div className="text-center space-y-2">
              <p>Lessons</p>
              <h1 className="font-semibold text-[42px] md:text-[68px]">
                2,100
              </h1>
            </div>
            <div className="text-center space-y-2">
              <p>Countries</p>
              <h1 className="font-semibold text-[42px] md:text-[68px]">21+</h1>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Community;
