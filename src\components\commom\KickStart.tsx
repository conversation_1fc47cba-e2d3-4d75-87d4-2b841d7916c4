"use client";
import Image from "next/image";
import React from "react";

const KickStart = () => {
  return (
    <div className="md:py-14 sm:py-8">
      <div className="bg-[#1F2630] text-white relative overflow-hidden">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 py-10 px-6 md:px-12 lg:px-20">
          <div className="space-y-6 flex flex-col justify-center md:text-left">
            <h1 className="text-[26px] sm:text-[30px] md:text-[38px] font-bold leading-snug">
              Kickstart Your Investing <br className="hidden md:block" />{" "}
              Journey in 5 Days
            </h1>
            <p className="text-[16px] sm:text-[17px] md:text-[18px] leading-relaxed">
              Join our free 5-day email series and learn the fundamentals of{" "}
              <br className="hidden md:block" /> smart investing — one simple
              lesson a day.
            </p>
          </div>
          <div className="flex items-center justify-center md:justify-end">
            <div className="bg-white text-black rounded-xl pl-4 pr-2 py-2 flex items-center justify-between gap-3 w-full sm:w-[90%] md:w-[70%]">
              <input
                type="email"
                className="w-full focus:outline-none py-2 text-[16px] sm:text-[17px] md:text-[18px]"
                placeholder="Email"
              />
              <button className="border border-[#1F2630] rounded-lg text-[#1F2630] font-semibold text-[14px] sm:text-[15px] md:text-[16px] px-3 sm:px-4 py-2 cursor-pointer whitespace-nowrap hover:bg-[#1F2630] hover:text-white transition">
                Submit
              </button>
            </div>
          </div>
        </div>
        <Image
          src="/commom/kick_start.svg"
          alt="kickStart"
          width={300}
          height={300}
          className="absolute right-0 bottom-0 max-w-[180px] sm:max-w-[100px] md:max-w-[260px] lg:max-w-[300px] opacity-80"
        />
      </div>
    </div>
  );
};

export default KickStart;
