"use client";
import Image from "next/image";
import React from "react";

const RealStoryCard = ({ stories }: any) => {
  return (
    <div className="w-full max-w-[480px] h-auto rounded-3xl overflow-hidden mx-auto bg-white shadow-[0_0_15px_0_rgba(0,0,0,0.1)] hover:scale-105 transition-transform">
      <div className="p-6 text-[#2A2A2A]">
        <div className="flex items-center gap-4 mb-4">
          <div className="md:w-[64px] md:h-[64px] sm:w-[50px] sm:h-[50px] rounded-full overflow-hidden border border-[#1F2630] flex-shrink-0">
            <Image
              src={stories.image}
              alt={stories.title}
              width={500}
              height={500}
              className="w-full h-full object-cover"
            />
          </div>
          <h2 className="text-lg sm:text-lg md:text-2xl font-semibold">
            {stories.title}
          </h2>
        </div>
        <p className="text-sm sm:text-sm md:text-lg leading-relaxed">
          {stories.description}
        </p>
      </div>
    </div>
  );
};

export default RealStoryCard;
