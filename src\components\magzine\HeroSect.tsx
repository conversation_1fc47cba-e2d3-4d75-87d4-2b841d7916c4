"use client";
import Image from "next/image";
import React from "react";

const HeroSect = () => {
  return (
    <div>
      <div className="flex flex-col items-center pt-8 md:space-y-16 sm:space-y-5">
        <h1 className="md:text-6xl sm:text-[36px] font-semibold text-[#1F2630] text-center leading-relaxed lg:w-[60%] sm:w-[90%]">
          Learn, Grow, and Stay Ahead with Piggy Magazine
        </h1>
        <div className="lg:w-[70%] sm:w-[90%] md:h-[270px] sm:h-[130px] rounded-3xl overflow-hidden shadow-[0_0_15px_0_rgba(0,0,0,0.5)]">
          <Image
            src="/magzine/mag_hero.png"
            alt="Magzine"
            width={500}
            height={500}
            className="w-full h-full object-cover"
          />
        </div>
      </div>
    </div>
  );
};

export default HeroSect;
