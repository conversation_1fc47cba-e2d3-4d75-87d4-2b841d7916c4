"use client";
import React from "react";
import { IoBookOutline } from "react-icons/io5";
import { LuClock } from "react-icons/lu";

const ExploreCard = ({ course }: any) => {
  return (
    <div
  className="
    relative w-full 
    h-[200px] sm:h-[240px] md:h-[260px] lg:h-[300px]
    rounded-3xl overflow-hidden 
    bg-cover bg-center
  "
  style={{ backgroundImage: `url(${course.image})` }}
>

      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />
      <div className="absolute bottom-0 left-0 right-0 p-6 text-white z-10">
        <h2 className="text-2xl font-semibold mb-3">{course.title}</h2>
        <div className="flex items-center gap-6 text-sm font-medium">
          <div className="flex items-center gap-2">
            <IoBookOutline size={18}/>
            <p>{course.lessons} Lessons</p>
          </div>
          <div className="flex items-center gap-2">
            <LuClock size={18}/>
            <p>{course.duration} Minutes</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExploreCard;
