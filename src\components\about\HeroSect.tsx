"use client";
import React from "react";

const HeroSect = () => {
  return (
    <div>
      <div className="flex flex-col items-center pt-8 md:space-y-16 sm:space-y-5">
        <h1 className="md:text-6xl sm:text-[36px] font-semibold text-[#1F2630] text-center leading-relaxed md:w-[60%] sm:w-[90%]">
          About Us
        </h1>
        <div
          className="relative lg:w-[70%] sm:w-[90%] h-[270px] rounded-3xl overflow-hidden bg-cover bg-center shadow-[0_0_15px_0_rgba(0,0,0,0.5)]"
          style={{ backgroundImage: `url(/about/banner.png)` }}
        >
          <div className="absolute inset-0 bg-gradient-to-t from-black/90  to-black/30 z-10" />

          <div className="absolute inset-0 flex flex-col items-center justify-center text-white md:px-10 sm:px-4 z-10">
            <div className="flex items-center sm:flex-col md:flex-row md:gap-8 sm:gap-4">
              <h2 className="flex-1 md:text-5xl sm:text-[38px] font-semibold leading-tight md:text-right sm:text-start w-full">
                Our <br className="hidden md:block" /> Vision
              </h2>
              <p className="md:text-2xl sm:text-[18px] flex-3">
                To create a world where every child grows up financially
                confident, with the knowledge and skills to make smart money
                decisions in the future.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSect;
