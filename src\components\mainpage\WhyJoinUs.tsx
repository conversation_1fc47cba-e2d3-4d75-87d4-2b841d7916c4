"use client";
import { whyJoinUsData } from "@/data/whyJoinus";
import Image from "next/image";
import React from "react";

const WhyJoinUs = () => {
  return (
    <div className="lg:px-20 md:px-10 sm:px-5 space-y-14 pb-16">
      <h1 className="md:text-[46px] sm:text-[22px] font-bold text-[#1F2630]">
        Why Join <PERSON>?
      </h1>
      <div className="grid md:grid-cols-2 sm:grid-cols-1 gap-10">
        {whyJoinUsData.map((item) => (
          <div
            key={item.id}
            className="bg-[#FFF6FC] p-10 rounded-3xl space-y-4"
          >
            <Image
              src={item.image}
              alt={`Why${item.id}`}
              width={60}
              height={60}
              className="w-10 h-10 md:w-16 md:h-16"
            />

            <h1 className="md:text-2xl sm:text-[18px] font-semibold text-[#FB5689]">
              {item.title}
            </h1>
            <p className="text-[#666666] md:text-base sm:text-sm">
              {item.description}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default WhyJoinUs;
