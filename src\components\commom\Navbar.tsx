"use client";
import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import { IoMdCheckmark } from "react-icons/io";

const Navbar = () => {
  const [scrolled, setScrolled] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const [langOpen, setLangOpen] = useState(false);
  const [language, setLanguage] = useState("ENGLISH");
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleNavigate = (href: string) => {
    router.push(href);
    setMenuOpen(false);
  };

  const toggleLanguage = (lang: string) => {
    setLanguage(lang);
    setLangOpen(false);
  };

  return (
    <div
      className={`flex items-center justify-between w-full py-6 px-5 md:px-10 sticky top-0 left-0 z-20 transition-colors duration-500 ${
        scrolled ? "bg-gray-100 shadow-md" : "bg-transparent"
      }`}
    >
      <div
        className="text-lg font-bold flex items-center space-x-2 cursor-pointer"
        onClick={() => handleNavigate("/")}
      >
        <Image src="/commom/logo.svg" alt="Logo" width={45} height={45} />
        <p className="font-semibold md:text-[20px] sm:text-[16px] text-[#1F2630]">
          Piggy Portfolio
        </p>
      </div>

      <div className="hidden md:flex space-x-7 text-[#2C3131]">
        <Link
          href="/mission"
          className={`${
            pathname === "/mission/"
              ? "text-[#1A74E8] font-bold"
              : "hover:text-gray-600"
          }`}
        >
          Mission
        </Link>
        <Link
          href="/magazine"
          className={`${
            pathname === "/magazine/"
              ? "text-[#1A74E8] font-bold"
              : "hover:text-gray-600"
          }`}
        >
          Magazine
        </Link>
        <Link
          href="/about-us"
          className={`${
            pathname === "/about-us/"
              ? "text-[#1A74E8] font-bold"
              : "hover:text-gray-600"
          }`}
        >
          About us
        </Link>
      </div>

      <div className="hidden md:flex items-center gap-8 relative">
        <div
          className="flex items-center gap-2 cursor-pointer relative"
          onClick={() => setLangOpen(!langOpen)}
        >
          <Image
            src="/commom/language_icon.svg"
            alt="Language"
            width={25}
            height={25}
          />
          <p className="font-semibold text-[#2C3131]">
            {language === "ENGLISH" ? "ENG" : "ROM"}
          </p>
          <Image src="/commom/arrow.svg" alt="Arrow" width={20} height={20} />
          {langOpen && (
            <div className="absolute top-10 right-0 bg-white text-black rounded-lg shadow-md w-40 flex flex-col">
              <div className="flex items-center justify-between px-4 py-2">
                <button
                  className={` text-left cursor-pointer ${
                    language === "ENGLISH" ? "" : ""
                  }`}
                  onClick={() => toggleLanguage("ENGLISH")}
                >
                  ENGLISH
                </button>
                <IoMdCheckmark
                  className={`${language === "ENGLISH" ? "" : "hidden"}`}
                />
              </div>
              <div className="flex items-center justify-between px-4 py-2">
                <button
                  className={` text-left cursor-pointer ${
                    language === "ROMANIAN" ? "" : ""
                  }`}
                  onClick={() => toggleLanguage("ROMANIAN")}
                >
                  ROMANIAN
                </button>
                <IoMdCheckmark
                  className={`${language === "ROMANIAN" ? "" : "hidden"}`}
                />
              </div>
            </div>
          )}
        </div>

        <div
          className="text-[#1F2630] font-semibold border-2 border-[#1F2630] px-5 py-3 rounded-xl hover:bg-[#1F2630] hover:text-white cursor-pointer"
          onClick={() => handleNavigate("/contact-us")}
        >
          Contact us
        </div>
      </div>

      <div
        className="md:hidden flex items-center gap-2"
        onClick={() => setMenuOpen(true)}
      >
        <Image
          src="/commom/language_icon.svg"
          alt="Language"
          width={25}
          height={25}
        />
        <p className="font-semibold text-[#2C3131] text-[14px]">ENG</p>
        <Image src="/commom/arrow.svg" alt="Arrow" width={20} height={20} />
        <Image
          src="/commom/menu.svg"
          alt="Menu"
          width={28}
          height={28}
          className="cursor-pointer"
        />
      </div>

      <div
        className={`fixed top-0 left-0 w-full h-[70%] bg-[#1F2630] z-50 flex flex-col items-center justify-center text-white space-y-8 transform transition-transform duration-500 ${
          menuOpen ? "translate-y-0" : "-translate-y-full"
        }`}
      >
        <button
          onClick={() => handleNavigate("/mission")}
          className={`text-xl font-normal ${
            pathname === "/mission/"
              ? "text-[#1A74E8] font-bold"
              : "hover:text-gray-600"
          }`}
        >
          Mission
        </button>
        <button
          onClick={() => handleNavigate("/magazine")}
          className={`text-xl font-normal ${
            pathname === "/magazine/"
              ? "text-[#1A74E8] font-bold"
              : "hover:text-gray-600"
          }`}
        >
          Magazine
        </button>
        <button
          onClick={() => handleNavigate("/about-us")}
          className={`text-xl font-normal ${
            pathname === "/about-us/"
              ? "text-[#1A74E8] font-bold"
              : "hover:text-gray-600"
          }`}
        >
          About us
        </button>
        <div
          className="text-white font-semibold border-2 border-white px-6 py-3 rounded-xl hover:bg-white hover:text-[#1F2630] cursor-pointer w-full text-center max-w-xs"
          onClick={() => handleNavigate("/contact-us")}
        >
          Contact us
        </div>
        <div onClick={() => setMenuOpen(false)}>
          <Image
            src="/commom/close_icon_white.svg"
            alt="Close"
            width={28}
            height={28}
          />
        </div>
      </div>
    </div>
  );
};

export default Navbar;
