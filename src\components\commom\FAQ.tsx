"use client";
import { faqData } from "@/data/faq";
import Image from "next/image";
import React, { useState } from "react";

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="md:px-20 sm:px-5 md:py-16 sm:py-6 grid sm:grid-cols-1 lg:grid-cols-2 gap-10">
      <div className="space-y-8">
        <h1 className="md:text-[46px] sm:text-[32px] font-bold text-[#1F2630]">
          Frequently Asked Questions
        </h1>
        <div className="space-y-6">
          {faqData.map((faq, index) => (
            <div
              key={index}
              className="bg-[#EFEFEF] rounded-xl p-4 md:p-6 cursor-pointer"
              onClick={() => toggleFAQ(index)}
            >
              <div className="flex justify-between items-center">
                <h2 className="text-[18px] md:text-[26px] font-medium text-[#2A2A2A]">
                  {faq.question}
                </h2>
                <div className="w-6 h-6 md:w-8 md:h-8 flex items-center justify-center bg-[#1F2630] text-white rounded-full transition-transform duration-300">
                  {openIndex === index ? "−" : "+"}
                </div>
              </div>
              <div
                className={`transition-all duration-500 overflow-hidden ${
                  openIndex === index ? "max-h-40 mt-3" : "max-h-0"
                }`}
              >
                <p className="text-[14px] md:text-[18px] text-[#7D7D7D]">
                  {faq.answer}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="flex justify-center items-center sm:mt-8 lg:mt-0">
        <Image
          src="/mainpage/faq_image1.svg"
          alt="FAQ Image"
          width={500}
          height={500}
          className="sm:w-[350px] h-auto md:w-[300px] lg:w-[500px] mx-auto"
        />
      </div>
    </div>
  );
};

export default FAQ;
